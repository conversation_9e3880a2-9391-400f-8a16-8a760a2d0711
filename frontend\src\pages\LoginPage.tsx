// LoginPage.tsx - K.A.R.E.N. (Knowledgeable Acquisition & Requisition Execution Navigator)
// Background image integration tested with flux-imagegen MCP server
// Successfully generated professional business backgrounds with expense management themes

import { useState, useEffect } from 'react'
import { Link as RouterLink, useNavigate, useLocation } from 'react-router-dom'
import {
  Box,
  Button,
  Container,
  FormControl,
  FormLabel,
  Input,
  VStack,
  Text,
  Heading,
  Link,
  Alert,
  AlertIcon,
  InputGroup,
  InputRightElement,
  IconButton,
  Divider,
  Flex,
  HStack,
  Checkbox,
  Stack,
} from '@chakra-ui/react'
import { FaFacebook, FaTwitter, FaInstagram, FaYoutube, FaEye, FaEyeSlash } from 'react-icons/fa'
import { useAuth } from '../contexts/AuthContext'

const LoginPage = () => {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [rememberMe, setRememberMe] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  const { login, user } = useAuth()
  const navigate = useNavigate()
  const location = useLocation()

  const from = location.state?.from?.pathname || '/dashboard'

  // Redirect if already logged in
  useEffect(() => {
    console.log('🎯 LoginPage mounted!')
    // Add a test function to window for debugging
    ;(window as any).testLogin = () => {
      console.log('🧪 Test function called!')
      handleSubmit({ preventDefault: () => {} } as any)
    }
    if (user) {
      navigate(from, { replace: true })
    }
  }, [user, navigate, from])

  const handleSubmit = async (e: React.FormEvent) => {
    console.log('🚀 Form submitted!', { email, password: '***' })
    e.preventDefault()
    setError('')
    setIsLoading(true)

    try {
      console.log('📞 Calling login function...')
      const success = await login(email, password)
      console.log('✅ Login result:', success)
      if (success) {
        navigate(from, { replace: true })
      }
    } catch (err: any) {
      console.error('❌ Login error:', err)
      setError(err.message || 'Login failed')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Box
      minH="100vh"
      position="relative"
      // Background generated using flux-imagegen MCP server for K.A.R.E.N. expense management theme
      // Professional office environment with expense compliance and enforcement elements
      bgImage="linear-gradient(135deg, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.4) 100%), url('/karen-expense-background.png')"
      bgSize="cover"
      bgPosition="center"
      bgRepeat="no-repeat"
    >
      <Flex minH="100vh">
        {/* Left Side - Welcome to K.A.R.E.N. */}
        <Flex
          flex="1"
          direction="column"
          justify="center"
          align="flex-start"
          p={16}
          color="white"
        >
          <VStack align="flex-start" spacing={6} maxW="500px">
            <Heading
              fontSize={{ base: "4xl", md: "6xl" }}
              fontWeight="bold"
              lineHeight="1.1"
              textShadow="2px 2px 4px rgba(0,0,0,0.3)"
            >
              Welcome to
              <br />
              K.A.R.E.N.
            </Heading>
            <Text
              fontSize="lg"
              color="whiteAlpha.900"
              textShadow="1px 1px 2px rgba(0,0,0,0.3)"
              maxW="400px"
            >
              Your Knowledgeable Acquisition & Requisition Execution Navigator is ready to streamline your procurement processes and guide efficient purchasing decisions.
            </Text>

            {/* Social Media Icons */}
            <HStack spacing={4} pt={4}>
              <IconButton
                aria-label="Facebook"
                icon={<FaFacebook />}
                variant="ghost"
                color="whiteAlpha.700"
                _hover={{ color: "white", transform: "translateY(-2px)" }}
                transition="all 0.2s"
              />
              <IconButton
                aria-label="Twitter"
                icon={<FaTwitter />}
                variant="ghost"
                color="whiteAlpha.700"
                _hover={{ color: "white", transform: "translateY(-2px)" }}
                transition="all 0.2s"
              />
              <IconButton
                aria-label="Instagram"
                icon={<FaInstagram />}
                variant="ghost"
                color="whiteAlpha.700"
                _hover={{ color: "white", transform: "translateY(-2px)" }}
                transition="all 0.2s"
              />
              <IconButton
                aria-label="YouTube"
                icon={<FaYoutube />}
                variant="ghost"
                color="whiteAlpha.700"
                _hover={{ color: "white", transform: "translateY(-2px)" }}
                transition="all 0.2s"
              />
            </HStack>
          </VStack>
        </Flex>

        {/* Right Side - Sign In Form */}
        <Flex
          flex="1"
          direction="column"
          justify="center"
          align="center"
          p={8}
        >
          <Box
            w="full"
            maxW="400px"
            bg="rgba(255, 255, 255, 0.95)"
            backdropFilter="blur(10px)"
            p={8}
            borderRadius="2xl"
            boxShadow="2xl"
            border="1px"
            borderColor="whiteAlpha.200"
          >
            <VStack spacing={6}>
              <VStack spacing={2}>
                <Heading size="lg" color="gray.800" textAlign="center">
                  Sign in
                </Heading>
                <Text color="gray.600" fontSize="sm" textAlign="center">
                  Enter your credentials to access your account
                </Text>
              </VStack>

              {error && (
                <Alert status="error" borderRadius="lg" fontSize="sm">
                  <AlertIcon />
                  {error}
                </Alert>
              )}

              <div style={{ width: '100%' }}>
                <VStack spacing={4}>
                  <FormControl isRequired>
                    <FormLabel color="gray.700" fontSize="sm">Email Address</FormLabel>
                    <InputGroup>
                      <Input
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        placeholder="Enter your email"
                        bg="white"
                        color="black"
                        border="2px"
                        borderColor="gray.200"
                        _hover={{ borderColor: 'gray.300' }}
                        _focus={{ borderColor: 'blue.500', boxShadow: '0 0 0 1px #3182ce' }}
                        borderRadius="lg"
                      />
                    </InputGroup>
                  </FormControl>

                  <FormControl isRequired>
                    <FormLabel color="gray.700" fontSize="sm">Password</FormLabel>
                    <InputGroup>
                      <Input
                        type={showPassword ? 'text' : 'password'}
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        placeholder="Enter your password"
                        bg="white"
                        color="black"
                        border="2px"
                        borderColor="gray.200"
                        _hover={{ borderColor: 'gray.300' }}
                        _focus={{ borderColor: 'blue.500', boxShadow: '0 0 0 1px #3182ce' }}
                        borderRadius="lg"
                      />
                      <InputRightElement>
                        <IconButton
                          aria-label={showPassword ? 'Hide password' : 'Show password'}
                          icon={showPassword ? <FaEyeSlash /> : <FaEye />}
                          onClick={() => setShowPassword(!showPassword)}
                          variant="ghost"
                          size="sm"
                          color="gray.500"
                          _hover={{ color: 'gray.700' }}
                        />
                      </InputRightElement>
                    </InputGroup>
                  </FormControl>

                  <HStack justify="space-between" w="full">
                    <Checkbox
                      isChecked={rememberMe}
                      onChange={(e) => setRememberMe(e.target.checked)}
                      colorScheme="blue"
                      size="sm"
                    >
                      <Text fontSize="sm" color="gray.600">Remember Me</Text>
                    </Checkbox>
                    <Link
                      fontSize="sm"
                      color="blue.500"
                      _hover={{ textDecoration: 'underline' }}
                    >
                      Lost your password?
                    </Link>
                  </HStack>

                  <button
                    type="button"
                    style={{
                      width: '100%',
                      padding: '12px',
                      backgroundColor: '#f56500',
                      color: 'white',
                      border: 'none',
                      borderRadius: '8px',
                      fontSize: '16px',
                      fontWeight: '600',
                      cursor: 'pointer'
                    }}
                    onClick={() => {
                      console.log('🔥 Button clicked!')
                      handleSubmit({ preventDefault: () => {} } as any)
                    }}
                    disabled={isLoading}
                  >
                    {isLoading ? 'Signing in...' : 'Sign in now'}
                  </button>

                  <Text color="gray.600" fontSize="sm" textAlign="center">
                    By clicking on "Sign in now" you agree to our{' '}
                    <Link color="blue.500" _hover={{ textDecoration: 'underline' }}>
                      Terms of Service
                    </Link>
                    {' | '}
                    <Link color="blue.500" _hover={{ textDecoration: 'underline' }}>
                      Privacy Policy
                    </Link>
                  </Text>

                  <Divider borderColor="gray.300" />

                  <Text color="gray.600" fontSize="sm" textAlign="center">
                    Don't have an account?{' '}
                    <Link
                      as={RouterLink}
                      to="/register"
                      color="blue.500"
                      fontWeight="semibold"
                      _hover={{ textDecoration: 'underline' }}
                    >
                      Sign up here
                    </Link>
                  </Text>
                </VStack>
              </div>
            </VStack>
          </Box>
        </Flex>
      </Flex>
    </Box>
  )
}

export default LoginPage
